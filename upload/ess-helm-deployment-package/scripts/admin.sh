#!/bin/bash

# ESS-HELM 增强管理脚本
# 版本: v1.0
# 基于: ESS-HELM 25.6.2 官方稳定版
# 功能: 用户管理 + 服务控制 + 注册控制 + 运维管理
# 作者: ESS-HELM 部署团队
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
NAMESPACE="matrix"
RELEASE_NAME="matrix-stack"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# API配置
SYNAPSE_ADMIN_API_BASE=""
ADMIN_ACCESS_TOKEN=""

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ESS-HELM 增强管理系统                      ║"
    echo "║                                                              ║"
    echo "║  🧑‍💼 用户管理 | 🔧 服务控制 | 📝 注册控制 | 🛠️ 运维管理        ║"
    echo "║                                                              ║"
    echo "║  基于 Synapse Admin API + Kubernetes 原生管理                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 初始化API配置
init_api_config() {
    # 获取Synapse服务地址
    local synapse_service=$(kubectl get service -n "$NAMESPACE" -l app.kubernetes.io/name=synapse -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")

    if [[ -n "$synapse_service" ]]; then
        SYNAPSE_ADMIN_API_BASE="http://${synapse_service}.${NAMESPACE}.svc.cluster.local:8008"
        info "已连接到Synapse服务: $synapse_service"
        # 尝试获取管理员访问令牌
        get_admin_access_token
        return 0
    else
        warning "无法找到Synapse服务"
        info "可能的原因："
        info "  • ESS-HELM尚未部署到Kubernetes集群"
        info "  • Synapse服务尚未启动"
        info "  • 命名空间 '$NAMESPACE' 不存在或无权限访问"
        info ""
        info "可用功能："
        info "  • 服务控制 (查看状态、重启服务等)"
        info "  • 运维管理 (资源监控、日志查看等)"
        info "  • 系统状态查看"
        info ""
        info "不可用功能："
        info "  • 用户管理 (需要Synapse Admin API)"
        info "  • 注册控制 (需要Synapse Admin API)"
        return 1
    fi
}

# 获取管理员访问令牌
get_admin_access_token() {
    # 尝试从配置文件读取
    local token_file="$HOME/.matrix-admin-token"
    if [[ -f "$token_file" ]]; then
        ADMIN_ACCESS_TOKEN=$(cat "$token_file")
        return 0
    fi
    
    # 提示用户输入
    echo -e "${YELLOW}请输入管理员访问令牌 (留空跳过):${NC}"
    read -r -s token
    if [[ -n "$token" ]]; then
        ADMIN_ACCESS_TOKEN="$token"
        echo "$token" > "$token_file"
        chmod 600 "$token_file"
    fi
}

# 检查Synapse API是否可用
check_synapse_api_available() {
    if [[ -z "$SYNAPSE_ADMIN_API_BASE" ]]; then
        error "Synapse服务不可用，此功能需要已部署的ESS-HELM系统"
        echo -e "${YELLOW}提示: 请先使用部署脚本部署ESS-HELM系统${NC}"
        echo -e "${YELLOW}按任意键返回主菜单...${NC}"
        read -r
        return 1
    fi
    return 0
}

# Synapse Admin API调用
call_synapse_api() {
    local method="$1"
    local endpoint="$2"
    local data="${3:-}"

    # 检查Synapse API是否可用
    if ! check_synapse_api_available; then
        return 1
    fi

    if [[ -z "$ADMIN_ACCESS_TOKEN" ]]; then
        error "未配置管理员访问令牌"
        return 1
    fi

    local url="${SYNAPSE_ADMIN_API_BASE}/_synapse/admin${endpoint}"
    local curl_args=(-s -X "$method" -H "Authorization: Bearer $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json")

    if [[ -n "$data" ]]; then
        curl_args+=(-d "$data")
    fi

    curl "${curl_args[@]}" "$url"
}

# 主菜单
show_main_menu() {
    while true; do
        clear
        show_welcome
        echo -e "${WHITE}请选择管理功能:${NC}"
        echo
        echo -e "${GREEN}1.${NC} 用户管理 (创建/删除/修改用户)"
        echo -e "${GREEN}2.${NC} 服务控制 (启动/停止/重启/扩缩容)"
        echo -e "${GREEN}3.${NC} 注册控制 (开关注册/令牌管理)"
        echo -e "${GREEN}4.${NC} 运维管理 (备份/日志/监控)"
        echo -e "${GREEN}5.${NC} 系统状态 (查看部署状态)"
        echo -e "${GREEN}6.${NC} 配置管理 (查看/修改配置)"
        echo -e "${GREEN}0.${NC} 退出程序"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                user_management_menu
                ;;
            2)
                service_control_menu
                ;;
            3)
                registration_control_menu
                ;;
            4)
                operations_management_menu
                ;;
            5)
                show_system_status
                ;;
            6)
                configuration_management_menu
                ;;
            0)
                echo -e "${GREEN}感谢使用 ESS-HELM 增强管理系统！${NC}"
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 用户管理菜单
user_management_menu() {
    # 检查Synapse API是否可用
    if ! check_synapse_api_available; then
        return
    fi

    while true; do
        clear
        echo -e "${CYAN}用户管理${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 创建用户"
        echo -e "${GREEN}2.${NC} 查看用户列表"
        echo -e "${GREEN}3.${NC} 重置用户密码"
        echo -e "${GREEN}4.${NC} 设置管理员权限 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}5.${NC} 停用用户 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}6.${NC} 查看用户详情 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                create_user
                ;;
            2)
                list_users
                ;;
            3)
                reset_user_password
                ;;
            4)
                show_function_not_implemented "设置管理员权限"
                ;;
            5)
                show_function_not_implemented "停用用户"
                ;;
            6)
                show_function_not_implemented "查看用户详情"
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 显示功能未实现提示
show_function_not_implemented() {
    local function_name="$1"
    echo -e "${CYAN}$function_name${NC}"
    echo
    warning "此功能正在开发中，敬请期待"
    echo -e "${YELLOW}按任意键返回...${NC}"
    read -r
}

# 创建用户
create_user() {
    echo -e "${CYAN}创建新用户${NC}"
    echo
    
    echo -e "${YELLOW}请输入用户ID (例如: @user:example.com):${NC}"
    read -r user_id
    
    echo -e "${YELLOW}请输入用户密码:${NC}"
    read -r -s password
    echo
    
    echo -e "${YELLOW}请输入显示名称 (可选):${NC}"
    read -r display_name
    
    echo -e "${YELLOW}是否设为管理员? (y/n):${NC}"
    read -r is_admin
    
    local admin_flag="false"
    if [[ "$is_admin" =~ ^[Yy]$ ]]; then
        admin_flag="true"
    fi
    
    local data=$(cat <<EOF
{
    "password": "$password",
    "displayname": "$display_name",
    "admin": $admin_flag
}
EOF
)
    
    info "正在创建用户: $user_id"
    local response=$(call_synapse_api "PUT" "/v2/users/$user_id" "$data")
    
    if echo "$response" | jq -e '.name' >/dev/null 2>&1; then
        success "用户创建成功"
        echo "$response" | jq .
    else
        error "用户创建失败"
        echo "$response"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看用户列表
list_users() {
    echo -e "${CYAN}用户列表${NC}"
    echo
    
    info "正在获取用户列表..."
    local response=$(call_synapse_api "GET" "/v2/users")
    
    if echo "$response" | jq -e '.users' >/dev/null 2>&1; then
        echo "$response" | jq -r '.users[] | "\(.name) - \(.displayname // "无显示名") - 管理员: \(.admin) - 停用: \(.deactivated)"'
    else
        error "获取用户列表失败"
        echo "$response"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 重置用户密码
reset_user_password() {
    echo -e "${CYAN}重置用户密码${NC}"
    echo
    
    echo -e "${YELLOW}请输入用户ID:${NC}"
    read -r user_id
    
    echo -e "${YELLOW}请输入新密码:${NC}"
    read -r -s new_password
    echo
    
    local data=$(cat <<EOF
{
    "new_password": "$new_password"
}
EOF
)
    
    info "正在重置密码: $user_id"
    local response=$(call_synapse_api "POST" "/v1/reset_password/$user_id" "$data")
    
    if [[ "$response" == "{}" ]]; then
        success "密码重置成功"
    else
        error "密码重置失败"
        echo "$response"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 服务控制菜单
service_control_menu() {
    while true; do
        clear
        echo -e "${CYAN}服务控制${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看服务状态"
        echo -e "${GREEN}2.${NC} 重启服务"
        echo -e "${GREEN}3.${NC} 停止服务"
        echo -e "${GREEN}4.${NC} 启动服务"
        echo -e "${GREEN}5.${NC} 扩缩容服务"
        echo -e "${GREEN}6.${NC} 查看服务日志"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_service_status
                ;;
            2)
                restart_service
                ;;
            3)
                stop_service
                ;;
            4)
                start_service
                ;;
            5)
                scale_service
                ;;
            6)
                show_service_logs
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 查看服务状态
show_service_status() {
    echo -e "${CYAN}服务状态${NC}"
    echo
    
    info "正在获取服务状态..."
    
    echo -e "${WHITE}Pods状态:${NC}"
    kubectl get pods -n "$NAMESPACE" -o wide
    echo
    
    echo -e "${WHITE}Services状态:${NC}"
    kubectl get services -n "$NAMESPACE"
    echo
    
    echo -e "${WHITE}Deployments状态:${NC}"
    kubectl get deployments -n "$NAMESPACE"
    echo
    
    echo -e "${WHITE}StatefulSets状态:${NC}"
    kubectl get statefulsets -n "$NAMESPACE"
    echo
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 重启服务
restart_service() {
    echo -e "${CYAN}重启服务${NC}"
    echo
    
    echo -e "${YELLOW}请选择要重启的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "6. PostgreSQL"
    echo "0. 返回"
    
    read -r service_choice
    
    local service_name=""
    case "$service_choice" in
        1)
            service_name="synapse"
            ;;
        2)
            service_name="element-web"
            ;;
        3)
            service_name="matrix-authentication-service"
            ;;
        4)
            service_name="matrix-rtc"
            ;;
        5)
            service_name="haproxy"
            ;;
        6)
            service_name="postgresql"
            ;;
        0)
            return
            ;;
        *)
            error "无效选择"
            return
            ;;
    esac
    
    info "正在重启服务: $service_name"
    
    # 查找对应的deployment或statefulset
    local deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    local statefulset=$(kubectl get statefulset -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    
    if [[ -n "$deployment" ]]; then
        kubectl rollout restart "$deployment" -n "$NAMESPACE"
        kubectl rollout status "$deployment" -n "$NAMESPACE"
        success "服务重启完成: $service_name"
    elif [[ -n "$statefulset" ]]; then
        kubectl rollout restart "$statefulset" -n "$NAMESPACE"
        kubectl rollout status "$statefulset" -n "$NAMESPACE"
        success "服务重启完成: $service_name"
    else
        error "未找到服务: $service_name"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 注册控制菜单
registration_control_menu() {
    # 检查Synapse API是否可用
    if ! check_synapse_api_available; then
        return
    fi

    while true; do
        clear
        echo -e "${CYAN}注册控制${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看注册状态 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}2.${NC} 开启/关闭注册 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}3.${NC} 创建注册令牌"
        echo -e "${GREEN}4.${NC} 查看注册令牌列表 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}5.${NC} 删除注册令牌 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-5):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_function_not_implemented "查看注册状态"
                ;;
            2)
                show_function_not_implemented "开启/关闭注册"
                ;;
            3)
                create_registration_token
                ;;
            4)
                show_function_not_implemented "查看注册令牌列表"
                ;;
            5)
                show_function_not_implemented "删除注册令牌"
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 创建注册令牌
create_registration_token() {
    echo -e "${CYAN}创建注册令牌${NC}"
    echo
    
    echo -e "${YELLOW}请输入令牌长度 (默认: 16):${NC}"
    read -r token_length
    token_length=${token_length:-16}
    
    echo -e "${YELLOW}请输入使用次数限制 (留空表示无限制):${NC}"
    read -r uses_allowed
    
    echo -e "${YELLOW}请输入过期时间 (天数，留空表示永不过期):${NC}"
    read -r expiry_days
    
    local data="{"
    if [[ -n "$uses_allowed" ]]; then
        data="$data\"uses_allowed\": $uses_allowed,"
    fi
    if [[ -n "$expiry_days" ]]; then
        local expiry_time=$(($(date +%s) + expiry_days * 86400))
        data="$data\"expiry_time\": ${expiry_time}000,"
    fi
    data="${data%,}}"
    
    info "正在创建注册令牌..."
    local response=$(call_synapse_api "POST" "/v1/registration_tokens/new" "$data")
    
    if echo "$response" | jq -e '.token' >/dev/null 2>&1; then
        success "注册令牌创建成功"
        echo "$response" | jq .
    else
        error "注册令牌创建失败"
        echo "$response"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 运维管理菜单
operations_management_menu() {
    while true; do
        clear
        echo -e "${CYAN}运维管理${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看系统资源使用情况"
        echo -e "${GREEN}2.${NC} 备份数据库"
        echo -e "${GREEN}3.${NC} 恢复数据库"
        echo -e "${GREEN}4.${NC} 查看详细日志"
        echo -e "${GREEN}5.${NC} 清理旧日志"
        echo -e "${GREEN}6.${NC} 系统健康检查"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_resource_usage
                ;;
            2)
                backup_database
                ;;
            3)
                restore_database
                ;;
            4)
                show_detailed_logs
                ;;
            5)
                cleanup_old_logs
                ;;
            6)
                system_health_check
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 查看系统资源使用情况
show_resource_usage() {
    echo -e "${CYAN}系统资源使用情况${NC}"
    echo
    
    info "正在获取资源使用情况..."
    
    echo -e "${WHITE}节点资源使用:${NC}"
    kubectl top nodes 2>/dev/null || echo "需要安装metrics-server"
    echo
    
    echo -e "${WHITE}Pod资源使用:${NC}"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "需要安装metrics-server"
    echo
    
    echo -e "${WHITE}存储使用情况:${NC}"
    kubectl get pvc -n "$NAMESPACE"
    echo
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 系统健康检查
system_health_check() {
    echo -e "${CYAN}系统健康检查${NC}"
    echo
    
    info "正在执行系统健康检查..."
    
    # 检查Pod状态
    echo -e "${WHITE}检查Pod健康状态:${NC}"
    local unhealthy_pods=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running -o name 2>/dev/null | wc -l)
    if [[ "$unhealthy_pods" -eq 0 ]]; then
        success "所有Pod运行正常"
    else
        warning "发现 $unhealthy_pods 个异常Pod"
        kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running
    fi
    echo
    
    # 检查服务连通性
    echo -e "${WHITE}检查服务连通性:${NC}"
    local services=$(kubectl get service -n "$NAMESPACE" -o name)
    for service in $services; do
        local service_name=$(echo "$service" | cut -d'/' -f2)
        echo -n "检查 $service_name: "
        if kubectl get endpoints -n "$NAMESPACE" "$service_name" -o jsonpath='{.subsets[*].addresses[*].ip}' | grep -q .; then
            echo -e "${GREEN}正常${NC}"
        else
            echo -e "${RED}异常${NC}"
        fi
    done
    echo
    
    success "健康检查完成"
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 显示系统状态
show_system_status() {
    if [[ "$1" == "--status" ]]; then
        # 命令行模式，直接显示状态
        show_service_status
        return
    fi
    
    # 交互模式
    show_service_status
}

# 主程序入口
main() {
    # 检查kubectl可用性
    if ! command -v kubectl &> /dev/null; then
        error "kubectl未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &>/dev/null; then
        error "无法连接到Kubernetes集群，请检查kubeconfig配置"
        exit 1
    fi
    
    # 初始化API配置（允许失败）
    init_api_config || true

    # 处理命令行参数
    if [[ $# -gt 0 && "$1" == "--status" ]]; then
        show_system_status --status
        exit 0
    fi

    # 显示主菜单
    show_main_menu
}

# 信号处理
trap 'error "脚本被中断"; exit 1' INT TERM

# 启动主程序
main "$@"
