#!/bin/bash

# 虚拟公网IP路由管理脚本
# 版本: v1.0
# 功能: 虚拟公网IP路由高可用，零停机切换
# 支持: LiveKit (**********) + TURN (**********)
# 作者: ESS-HELM 部署团队
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/tmp/ess-helm-virtual-ip-routing.log"
PID_FILE="/tmp/ess-helm-virtual-ip-routing.pid"
CONFIG_FILE="/tmp/ess-helm-virtual-ip-config.json"

# 确保临时文件目录存在且可写
ensure_temp_dir() {
    if [[ ! -w "/tmp" ]]; then
        error "无法写入/tmp目录，请检查权限"
        exit 1
    fi
}

# 默认虚拟IP配置
DEFAULT_LIVEKIT_IP="**********"
DEFAULT_TURN_IP="**********"
DEFAULT_UPDATE_INTERVAL=10
DEFAULT_FAILOVER_TIMEOUT=60
DEFAULT_GRACEFUL_SWITCH_TIMEOUT=30

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "虚拟公网IP路由管理脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "可选参数:"
    echo "  --livekit-ip IP              LiveKit虚拟IP (默认: **********)"
    echo "  --turn-ip IP                 TURN虚拟IP (默认: **********)"
    echo "  --update-interval SECONDS   路由更新间隔 (默认: 10)"
    echo "  --failover-timeout SECONDS  故障转移超时 (默认: 60)"
    echo "  --graceful-timeout SECONDS  优雅切换超时 (默认: 30)"
    echo "  --update-wan-ip IP           更新WAN IP地址"
    echo "  --daemon                     以守护进程模式运行"
    echo "  --stop                       停止守护进程"
    echo "  --status                     查看运行状态"
    echo "  --test                       测试路由配置"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --daemon"
    echo "  $0 --update-wan-ip ***********"
    echo "  $0 --test"
    echo "  $0 --stop"
    echo "  $0 --status"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --livekit-ip)
                LIVEKIT_IP="$2"
                shift 2
                ;;
            --turn-ip)
                TURN_IP="$2"
                shift 2
                ;;
            --update-interval)
                UPDATE_INTERVAL="$2"
                shift 2
                ;;
            --failover-timeout)
                FAILOVER_TIMEOUT="$2"
                shift 2
                ;;
            --graceful-timeout)
                GRACEFUL_SWITCH_TIMEOUT="$2"
                shift 2
                ;;
            --update-wan-ip)
                WAN_IP="$2"
                update_wan_ip_route "$WAN_IP"
                exit 0
                ;;
            --daemon)
                DAEMON_MODE=true
                shift
                ;;
            --stop)
                stop_daemon
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            --test)
                test_routing_configuration
                exit 0
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认值
    LIVEKIT_IP="${LIVEKIT_IP:-$DEFAULT_LIVEKIT_IP}"
    TURN_IP="${TURN_IP:-$DEFAULT_TURN_IP}"
    UPDATE_INTERVAL="${UPDATE_INTERVAL:-$DEFAULT_UPDATE_INTERVAL}"
    FAILOVER_TIMEOUT="${FAILOVER_TIMEOUT:-$DEFAULT_FAILOVER_TIMEOUT}"
    GRACEFUL_SWITCH_TIMEOUT="${GRACEFUL_SWITCH_TIMEOUT:-$DEFAULT_GRACEFUL_SWITCH_TIMEOUT}"
    DAEMON_MODE="${DAEMON_MODE:-false}"
}

# 初始化配置
init_configuration() {
    # 创建配置文件
    cat > "$CONFIG_FILE" << EOF
{
    "livekit": {
        "virtualIp": "$LIVEKIT_IP",
        "enabled": true,
        "routingMode": "dynamic",
        "healthCheck": {
            "enabled": true,
            "interval": "30s",
            "timeout": "10s"
        }
    },
    "turn": {
        "virtualIp": "$TURN_IP",
        "enabled": true,
        "routingMode": "dynamic",
        "healthCheck": {
            "enabled": true,
            "interval": "30s",
            "timeout": "10s"
        }
    },
    "routeManager": {
        "enabled": true,
        "updateInterval": "${UPDATE_INTERVAL}s",
        "failoverTimeout": "${FAILOVER_TIMEOUT}s"
    },
    "zeroDowntime": {
        "enabled": true,
        "gracefulSwitchTimeout": "${GRACEFUL_SWITCH_TIMEOUT}s"
    }
}
EOF
    
    success "配置文件已创建: $CONFIG_FILE"
}

# 检查系统环境
check_environment() {
    info "检查系统环境..."
    
    # 检查是否有root权限 (路由操作需要)
    if [[ $EUID -ne 0 ]]; then
        warning "某些路由操作可能需要root权限"
    fi
    
    # 检查必要工具
    local required_tools=("ip" "iptables" "curl")
    local missing_tools=()
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "缺少必要工具: ${missing_tools[*]}"
        return 1
    fi
    
    success "环境检查通过"
}

# 获取当前WAN IP
get_current_wan_ip() {
    local wan_ip=""
    
    # 方法1: 从Router WAN IP检测脚本获取
    if [[ -f "${SCRIPT_DIR}/current-wan-ip.txt" ]]; then
        wan_ip=$(cat "${SCRIPT_DIR}/current-wan-ip.txt" 2>/dev/null || echo "")
    fi
    
    # 方法2: 从网络接口获取
    if [[ -z "$wan_ip" ]]; then
        wan_ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo "")
    fi
    
    # 方法3: 从默认路由获取
    if [[ -z "$wan_ip" ]]; then
        wan_ip=$(ip route show default 2>/dev/null | grep -oP 'src \K\S+' || echo "")
    fi
    
    if [[ -n "$wan_ip" && "$wan_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        echo "$wan_ip"
    else
        error "无法获取当前WAN IP地址"
        return 1
    fi
}

# 创建虚拟IP路由规则
create_virtual_ip_routes() {
    local wan_ip="$1"
    
    info "创建虚拟IP路由规则 (WAN IP: $wan_ip)"
    
    # LiveKit虚拟IP路由
    if [[ "$LIVEKIT_IP" != "$wan_ip" ]]; then
        info "配置LiveKit虚拟IP路由: $LIVEKIT_IP -> $wan_ip"
        
        # 添加DNAT规则
        iptables -t nat -C PREROUTING -d "$LIVEKIT_IP" -j DNAT --to-destination "$wan_ip" 2>/dev/null || \
        iptables -t nat -A PREROUTING -d "$LIVEKIT_IP" -j DNAT --to-destination "$wan_ip"
        
        # 添加SNAT规则
        iptables -t nat -C POSTROUTING -s "$wan_ip" -j SNAT --to-source "$LIVEKIT_IP" 2>/dev/null || \
        iptables -t nat -A POSTROUTING -s "$wan_ip" -j SNAT --to-source "$LIVEKIT_IP"
    fi
    
    # TURN虚拟IP路由
    if [[ "$TURN_IP" != "$wan_ip" ]]; then
        info "配置TURN虚拟IP路由: $TURN_IP -> $wan_ip"
        
        # 添加DNAT规则
        iptables -t nat -C PREROUTING -d "$TURN_IP" -j DNAT --to-destination "$wan_ip" 2>/dev/null || \
        iptables -t nat -A PREROUTING -d "$TURN_IP" -j DNAT --to-destination "$wan_ip"
        
        # 添加SNAT规则
        iptables -t nat -C POSTROUTING -s "$wan_ip" -j SNAT --to-source "$TURN_IP" 2>/dev/null || \
        iptables -t nat -A POSTROUTING -s "$wan_ip" -j SNAT --to-source "$TURN_IP"
    fi
    
    success "虚拟IP路由规则创建完成"
}

# 清理虚拟IP路由规则
cleanup_virtual_ip_routes() {
    info "清理虚拟IP路由规则..."
    
    # 清理LiveKit相关规则
    iptables -t nat -D PREROUTING -d "$LIVEKIT_IP" -j DNAT --to-destination "$1" 2>/dev/null || true
    iptables -t nat -D POSTROUTING -s "$1" -j SNAT --to-source "$LIVEKIT_IP" 2>/dev/null || true
    
    # 清理TURN相关规则
    iptables -t nat -D PREROUTING -d "$TURN_IP" -j DNAT --to-destination "$1" 2>/dev/null || true
    iptables -t nat -D POSTROUTING -s "$1" -j SNAT --to-source "$TURN_IP" 2>/dev/null || true
    
    success "虚拟IP路由规则清理完成"
}

# 更新WAN IP路由
update_wan_ip_route() {
    local new_wan_ip="$1"
    local old_wan_ip=""
    
    # 获取当前WAN IP
    old_wan_ip=$(get_current_wan_ip || echo "")
    
    if [[ "$new_wan_ip" == "$old_wan_ip" ]]; then
        info "WAN IP未变化，无需更新路由"
        return 0
    fi
    
    info "更新WAN IP路由: $old_wan_ip -> $new_wan_ip"
    
    # 优雅切换: 先添加新规则，再删除旧规则
    if [[ -n "$old_wan_ip" ]]; then
        info "开始优雅切换路由规则..."
        
        # 创建新路由规则
        create_virtual_ip_routes "$new_wan_ip"
        
        # 等待一段时间确保新规则生效
        sleep "$GRACEFUL_SWITCH_TIMEOUT"
        
        # 清理旧路由规则
        cleanup_virtual_ip_routes "$old_wan_ip"
    else
        # 直接创建新规则
        create_virtual_ip_routes "$new_wan_ip"
    fi
    
    # 更新当前WAN IP记录
    echo "$new_wan_ip" > "${SCRIPT_DIR}/current-wan-ip.txt"
    
    success "WAN IP路由更新完成: $new_wan_ip"
    
    # 执行健康检查
    perform_health_check "$new_wan_ip"
}

# 执行健康检查
perform_health_check() {
    local wan_ip="$1"
    
    info "执行虚拟IP健康检查..."
    
    # 检查LiveKit虚拟IP
    if ping -c 1 -W 5 "$LIVEKIT_IP" &>/dev/null; then
        success "LiveKit虚拟IP ($LIVEKIT_IP) 健康检查通过"
    else
        warning "LiveKit虚拟IP ($LIVEKIT_IP) 健康检查失败"
    fi
    
    # 检查TURN虚拟IP
    if ping -c 1 -W 5 "$TURN_IP" &>/dev/null; then
        success "TURN虚拟IP ($TURN_IP) 健康检查通过"
    else
        warning "TURN虚拟IP ($TURN_IP) 健康检查失败"
    fi
    
    # 检查路由规则
    local livekit_rules=$(iptables -t nat -L PREROUTING -n | grep "$LIVEKIT_IP" | wc -l)
    local turn_rules=$(iptables -t nat -L PREROUTING -n | grep "$TURN_IP" | wc -l)
    
    info "当前路由规则数量 - LiveKit: $livekit_rules, TURN: $turn_rules"
}

# 测试路由配置
test_routing_configuration() {
    echo -e "${CYAN}测试虚拟IP路由配置${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查环境
    check_environment
    
    # 获取当前WAN IP
    local wan_ip=$(get_current_wan_ip)
    if [[ -n "$wan_ip" ]]; then
        echo -e "当前WAN IP: ${WHITE}$wan_ip${NC}"
    else
        echo -e "当前WAN IP: ${RED}无法获取${NC}"
        return 1
    fi
    
    # 显示虚拟IP配置
    echo -e "LiveKit虚拟IP: ${WHITE}$LIVEKIT_IP${NC}"
    echo -e "TURN虚拟IP: ${WHITE}$TURN_IP${NC}"
    echo
    
    # 检查当前路由规则
    echo -e "${WHITE}当前iptables NAT规则:${NC}"
    iptables -t nat -L PREROUTING -n | grep -E "($LIVEKIT_IP|$TURN_IP)" || echo "未找到相关规则"
    echo
    
    # 执行健康检查
    perform_health_check "$wan_ip"
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    success "路由配置测试完成"
}

# 路由监控循环
routing_monitor_loop() {
    info "开始虚拟IP路由监控循环 (间隔: ${UPDATE_INTERVAL}秒)"
    
    local last_wan_ip=""
    
    while true; do
        local current_wan_ip=$(get_current_wan_ip || echo "")
        
        if [[ -n "$current_wan_ip" ]]; then
            if [[ "$current_wan_ip" != "$last_wan_ip" ]]; then
                info "检测到WAN IP变化: $last_wan_ip -> $current_wan_ip"
                update_wan_ip_route "$current_wan_ip"
                last_wan_ip="$current_wan_ip"
            else
                info "WAN IP监控正常: $current_wan_ip"
            fi
        else
            warning "无法获取当前WAN IP"
        fi
        
        sleep "$UPDATE_INTERVAL"
    done
}

# 启动守护进程
start_daemon() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            warning "虚拟IP路由管理服务已在运行 (PID: $pid)"
            return 1
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    info "启动虚拟IP路由管理守护进程..."
    
    # 检查环境
    check_environment
    
    # 初始化配置
    init_configuration
    
    # 启动后台进程
    nohup "$0" \
        --livekit-ip "$LIVEKIT_IP" \
        --turn-ip "$TURN_IP" \
        --update-interval "$UPDATE_INTERVAL" \
        --failover-timeout "$FAILOVER_TIMEOUT" \
        --graceful-timeout "$GRACEFUL_SWITCH_TIMEOUT" \
        > "$LOG_FILE" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    success "守护进程已启动 (PID: $pid)"
    success "日志文件: $LOG_FILE"
}

# 停止守护进程
stop_daemon() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            info "停止虚拟IP路由管理守护进程 (PID: $pid)..."
            kill "$pid"
            rm -f "$PID_FILE"
            success "守护进程已停止"
        else
            warning "守护进程未运行"
            rm -f "$PID_FILE"
        fi
    else
        warning "未找到PID文件，守护进程可能未运行"
    fi
}

# 查看运行状态
show_status() {
    echo -e "${CYAN}虚拟IP路由管理服务状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "状态: ${GREEN}运行中${NC} (PID: $pid)"
        else
            echo -e "状态: ${RED}已停止${NC} (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        echo -e "状态: ${RED}未运行${NC}"
    fi
    
    local wan_ip=$(get_current_wan_ip || echo "未知")
    echo -e "当前WAN IP: ${WHITE}$wan_ip${NC}"
    echo -e "LiveKit虚拟IP: ${WHITE}$LIVEKIT_IP${NC}"
    echo -e "TURN虚拟IP: ${WHITE}$TURN_IP${NC}"
    
    if [[ -f "$CONFIG_FILE" ]]; then
        echo -e "配置文件: ${WHITE}$CONFIG_FILE${NC}"
    fi
    
    if [[ -f "$LOG_FILE" ]]; then
        echo -e "日志文件: ${WHITE}$LOG_FILE${NC}"
        echo -e "最近日志:"
        tail -5 "$LOG_FILE" | sed 's/^/  /'
    fi
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 主函数
main() {
    # 确保临时目录可用
    ensure_temp_dir

    # 解析参数
    parse_arguments "$@"

    # 创建日志文件
    touch "$LOG_FILE" || {
        error "无法创建日志文件: $LOG_FILE"
        exit 1
    }

    if [[ "$DAEMON_MODE" == "true" ]]; then
        start_daemon
    else
        # 直接运行监控循环
        routing_monitor_loop
    fi
}

# 日志归档函数
archive_logs() {
    local service_dir="${SERVICE_DIR:-/opt/ess-helm}"
    local log_archive_dir="$service_dir/logs/virtual-ip-routing"

    if [[ -f "$LOG_FILE" ]]; then
        mkdir -p "$log_archive_dir"
        cp "$LOG_FILE" "$log_archive_dir/virtual-ip-routing-$(date +%Y%m%d-%H%M%S).log" 2>/dev/null || true
    fi
}

# 信号处理
cleanup() {
    info "收到退出信号，正在清理..."

    # 归档日志
    archive_logs

    if [[ -f "$PID_FILE" ]]; then
        rm -f "$PID_FILE"
    fi
    exit 0
}

trap cleanup INT TERM

# 启动主程序
main "$@"
