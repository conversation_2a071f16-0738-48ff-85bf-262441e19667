# Matrix Hybird Deploy

## 📋 项目概述

ESS-HELM一键部署系统是基于官方ESS-HELM 25.6.2稳定版的完整Matrix通信服务部署解决方案。该系统以最小化改造为核心原则，实现了Router WAN IP自动检测、虚拟公网IP路由高可用和增强管理功能。

**🎯 核心特性**:
- 🚀 **一键部署**: 支持 `bash <(curl -sSL <URL>/setup.sh)` 完全自动化部署
- 🌐 **Router WAN IP检测**: 5秒检测间隔，完全本地化，摒弃外部服务依赖
- 🔀 **虚拟公网IP路由**: ********** (LiveKit) + ********** (TURN) 高可用路由
- 🛠️ **增强管理功能**: 基于Synapse Admin API和Kubernetes原生管理
- 🎨 **用户友好界面**: 技术小白友好的中文交互式配置系统
- 🔒 **完全独立**: 支持在全新代码仓库中完全独立部署和运行

**版本**: v1.0
**基于**: ESS-HELM 25.6.2 (官方稳定版)
**发布时间**: 2025-06-20

## 🏗️ 系统架构

```
ESS-HELM 一键部署系统
├── 主入口脚本 (setup.sh)
│   ├── 环境检测和依赖安装
│   ├── 用户友好的中文交互界面
│   └── 智能默认值配置
├── 部署脚本
│   ├── external.sh (外部服务器部署)
│   ├── internal.sh (内部服务器部署)
│   └── admin.sh (增强管理功能)
├── 核心功能
│   ├── Router WAN IP自动检测
│   ├── 虚拟公网IP路由管理
│   └── ESS-HELM服务部署
└── 管理功能
    ├── 用户管理 (CRUD操作)
    ├── 服务控制 (启停/扩缩容)
    ├── 注册控制 (令牌管理)
    └── 运维管理 (备份/监控)
```

## 🚀 快速开始

### 一键部署 (推荐)

```bash
# 下载并运行一键部署脚本
bash <(curl -sSL https://raw.githubusercontent.com/your-repo/ess/main/setup.sh)
```

### 手动部署

```bash
# 1. 克隆仓库
git clone https://github.com/your-repo/ess-helm-deployment-package.git
cd ess-helm-deployment-package

# 2. 运行主入口脚本
chmod +x setup.sh
./setup.sh
```

## 📋 系统要求

### 硬件要求
- **CPU**: 最少2核，推荐4核
- **内存**: 最少4GB，推荐8GB
- **存储**: 最少50GB可用空间

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **Kubernetes**: 版本 >= 1.20
- **Helm**: 版本 >= 3.0
- **Docker**: 版本 >= 20.10
- **kubectl**: 与Kubernetes集群版本兼容

### 网络要求
- **外部部署**: 公网IP地址，开放端口8443、8448、3478、5349
- **内部部署**: 内网访问，可选开放联邦端口8448

## 🎯 部署模式

### 1. 外部服务器部署
- **适用场景**: 需要公网访问的环境
- **核心功能**: Router WAN IP自动检测 + 虚拟公网IP路由
- **特色功能**:
  - 5秒检测间隔的WAN IP监控
  - 零停机IP切换
  - 虚拟公网IP高可用路由

### 2. 内部服务器部署
- **适用场景**: 内网环境，企业内部使用
- **核心功能**: 简化网络配置，增强安全性
- **特色功能**:
  - 自签名证书自动生成
  - 内网网络策略优化
  - 简化的服务配置

## 🛠️ 核心功能

### Router WAN IP自动检测
```yaml
# 配置示例
routerWanIpDetection:
  enabled: true
  mode: "wan-ip-only"
  router:
    host: "***********"
    username: "admin"
    password: "your-password"
    port: 8728
  detection:
    schedule: "*/5 * * * * *"  # 5秒检测间隔
    wanInterface: "ether1"
  validation:
    enableWanIpVerification: true
    disableExternalServices: true  # 完全本地化
```

### 虚拟公网IP路由高可用
```yaml
# 配置示例
virtualPublicIpRouting:
  enabled: true
  livekit:
    virtualPublicIp: "**********"
    enabled: true
  turn:
    virtualPublicIp: "**********"
    enabled: true
  zeroDowntime:
    enabled: true
    gracefulSwitchTimeout: "30s"
```

### 增强管理功能
- **用户管理**: 基于Synapse Admin API的完整CRUD操作
- **服务控制**: 基于Kubernetes的服务生命周期管理
- **注册控制**: 注册开关、令牌管理、邀请机制
- **运维管理**: 备份恢复、日志查看、监控告警

## 📁 项目结构

```
ess-helm-deployment-package/
├── setup.sh                           # 主入口脚本
├── scripts/                           # 脚本目录
│   ├── external.sh                    # 外部服务器部署脚本
│   ├── internal.sh                    # 内部服务器部署脚本
│   ├── admin.sh                       # 增强管理脚本
│   ├── router-wan-ip-detector.sh      # Router WAN IP检测脚本
│   └── virtual-public-ip-route-manager.sh # 虚拟IP路由管理脚本
├── charts/matrix-stack/               # Helm Charts配置
│   ├── values.yaml                    # 主配置文件
│   ├── values-router-wan-ip-detection.yaml
│   ├── values-virtual-public-ip-routing.yaml
│   └── values-internal-server.yaml
├── configs/                           # 配置文件目录
│   └── external-server-nginx.conf     # 外部服务器Nginx配置
├── docs/                              # 文档目录
│   ├── deployment-guide.md            # 部署指南
│   ├── admin-guide.md                 # 管理指南
│   └── troubleshooting.md             # 故障排除指南
└── README.md                          # 项目说明文档
```

## 🎨 用户界面

### 主菜单
```
╔══════════════════════════════════════════════════════════════╗
║                    Matrix Hybird Deploy                      ║
║                                                              ║
║  版本: 25.6.2 (官方稳定版)                                   ║
║  功能: Router WAN IP检测 + 虚拟公网IP路由 + 增强管理           ║
║  支持: 外部服务器 + 内部服务器 + 完整管理功能                  ║
║                                                              ║
║  🚀 一键部署 | 🛠️ 智能配置 | 🔧 增强管理                     ║
╚══════════════════════════════════════════════════════════════╝

请选择部署模式:

1. 外部服务器部署 (公网访问，支持Router WAN IP检测)
2. 内部服务器部署 (内网访问，简化配置)
3. 管理现有部署 (启动/停止/重启/监控)
4. 查看部署状态
5. 查看帮助文档
0. 退出程序
```

### 配置收集界面
- **智能默认值**: 用户留空时自动采用预设默认值
- **实时验证**: 域名格式、端口范围、路径有效性验证
- **中文提示**: 技术小白友好的清晰中文提示
- **统一导航**: 选项"0"始终表示返回上级菜单

## 🔧 管理功能

### 用户管理
```bash
# 启动管理界面
./scripts/admin.sh

# 功能包括:
# - 创建用户
# - 查看用户列表
# - 重置用户密码
# - 设置管理员权限
# - 停用用户
# - 查看用户详情
```

### 服务控制
```bash
# 服务管理功能:
# - 查看服务状态
# - 重启服务
# - 停止/启动服务
# - 扩缩容服务
# - 查看服务日志
```

### 注册控制
```bash
# 注册管理功能:
# - 查看注册状态
# - 开启/关闭注册
# - 创建注册令牌
# - 查看令牌列表
# - 删除注册令牌
```

## 📊 监控和维护

### 系统监控
- **资源使用监控**: CPU、内存、存储使用情况
- **服务健康检查**: 自动检测服务状态
- **网络连通性测试**: 验证各服务间连接
- **性能指标收集**: 响应时间、吞吐量等

### 日志管理
- **集中日志收集**: 所有服务日志统一管理
- **日志轮换**: 自动清理旧日志文件
- **错误告警**: 关键错误自动通知

### 备份恢复
- **自动备份**: 定时备份数据库和配置
- **一键恢复**: 简化的恢复流程
- **备份验证**: 确保备份文件完整性

## 🔒 安全特性

### 网络安全
- **网络策略**: Kubernetes原生网络隔离
- **防火墙配置**: 自动配置必要的防火墙规则
- **SSL/TLS加密**: 全链路加密通信

### 访问控制
- **RBAC权限**: 基于角色的访问控制
- **API访问控制**: 管理API访问限制
- **审计日志**: 完整的操作审计记录

## 📚 文档

- **[部署指南](./docs/deployment-guide.md)** - 详细的部署步骤和配置说明
- **[管理指南](./docs/admin-guide.md)** - 完整的管理和维护指南
- **[故障排除指南](./docs/troubleshooting.md)** - 常见问题解决方案

## 🤝 贡献

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。详见 [LICENSE](LICENSE) 文件。

## 🆘 支持

### 获取帮助
- **查看文档**: 详细的部署和管理指南
- **运行诊断**: 使用内置诊断工具
- **社区支持**: Matrix社区 #synapse:matrix.org

### 问题报告
如果遇到问题，请提供以下信息：
- 系统环境信息
- 详细的错误描述
- 相关日志文件
- 重现步骤

## 🎉 致谢

- **Element团队**: 感谢提供优秀的ESS-HELM项目
- **Matrix社区**: 感谢开源的Matrix协议和生态
- **Kubernetes社区**: 感谢强大的容器编排平台

---

**⚡ 快速开始**: `bash <(curl -sSL <URL>/setup.sh)`

**📖 完整文档**: [部署指南](./docs/deployment-guide.md) | [管理指南](./docs/admin-guide.md) | [故障排除](./docs/troubleshooting.md)

**🔗 相关链接**: [ESS-HELM官方](https://github.com/element-hq/ess-helm) | [Matrix协议](https://matrix.org) | [Element客户端](https://element.io)
